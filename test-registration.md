# Registration API Test Documentation

## Updated Registration Workflow

The registration endpoint at `src/app/api/v1/register/route.ts` now implements the following workflow:

### 1. User Registration Process
1. **User Creation**: Creates a new user with OWNER role
2. **Organization Creation**: Creates a new organization with the user as owner
3. **Organization Chat Creation**: Automatically creates an organization chat using `createCompleteOrganizationChat`
4. **Default Department Creation**: Creates a "default" department
5. **Department Membership**: Adds the user to the default department

### 2. Transaction Handling
All steps are wrapped in a single database transaction to ensure atomicity. If any step fails, the entire registration is rolled back.

### 3. Organization Chat Setup
- **Chat Name**: Uses the new naming convention (just the organization name, no "Organization Chat" suffix)
- **Creator as Admin**: The newly registered user is automatically added as an admin to the organization chat
- **Member Addition**: The `addOrganizationMembersToChat` function is called but won't add anyone else since there are no other members yet

### 4. API Response
The response now includes:
- User information
- Organization information  
- Department information
- **Organization Chat information** (new)
- **Chat Membership information** (new)

### 5. Error Handling
Enhanced error handling provides specific error messages for different failure scenarios:
- Organization creation failure
- Chat creation failure
- Department creation failure
- Duplicate user errors

## Example API Response

```json
{
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+1234567890",
      "role": "owner"
    },
    "organization": {
      "id": 1,
      "name": "My Company"
    },
    "department": {
      "id": 1,
      "name": "default"
    },
    "organizationChat": {
      "id": 1,
      "name": "My Company",
      "chatType": "ORGANIZATION"
    },
    "chatMembership": {
      "isAdmin": true,
      "joinedAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## Testing the Implementation

To test this implementation:

1. **Send a POST request** to `/api/v1/register` with:
   ```json
   {
     "email": "<EMAIL>",
     "password": "password123",
     "firstName": "Test",
     "lastName": "User",
     "phone": "+1234567890",
     "organizationName": "Test Organization"
   }
   ```

2. **Verify the response** includes all the new fields

3. **Check the database** to ensure:
   - User is created with OWNER role
   - Organization is created with user as owner
   - Organization chat is created with correct name
   - User is added to chat_users table as admin
   - Default department is created
   - User is added to department_members table

## Benefits

- **Immediate Chat Access**: New users can immediately access their organization chat
- **Consistent Naming**: Uses the new naming convention for organization chats
- **Atomic Operations**: All-or-nothing approach ensures data consistency
- **Admin Privileges**: User gets admin privileges in their organization chat from the start
- **Scalable**: When more users join the organization, they'll be automatically added to the existing chat
